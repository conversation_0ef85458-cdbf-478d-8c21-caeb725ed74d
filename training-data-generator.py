#!/usr/bin/env python3
"""
AI-Powered Training Data Generator for S32DS SWTBot Code Generation
Generates training data using multiple AI approaches for RAG and Fine-tuning
"""

import json
import yaml
import asyncio
import aiohttp
import argparse
from typing import List, Dict, Any
from dataclasses import dataclass
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class S32DSTestCase:
    """S32DS test case structure"""
    name: str
    description: str
    soc: str
    package: str
    category: str
    steps: List[Dict[str, Any]]
    expected_swtbot_code: str
    complexity: str = "medium"

class TrainingDataGenerator:
    """Main class for generating S32DS training data using AI"""
    
    def __init__(self, config_path: str = "config/data_generation_config.yaml"):
        self.config = self.load_config(config_path)
        self.openai_client = None
        self.anthropic_client = None
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Default configuration for data generation"""
        return {
            "s32ds_socs": ["S32K144", "S32K146", "S32K148", "S32G274A", "S32G399A"],
            "packages": ["LQFP100", "LQFP144", "MAPBGA100", "LQFP176"],
            "categories": [
                "project_creation", "build_configuration", "debug_setup",
                "peripheral_config", "code_generation", "flash_programming"
            ],
            "complexity_levels": ["simple", "medium", "complex"],
            "generation_models": {
                "primary": "gpt-4-turbo",
                "fallback": "claude-3-sonnet"
            },
            "output_formats": ["json", "yaml", "csv"]
        }

    async def generate_s32ds_test_scenarios(self, count: int = 100) -> List[Dict[str, Any]]:
        """Generate S32DS test scenarios using AI"""
        logger.info(f"Generating {count} S32DS test scenarios...")
        
        scenarios = []
        for i in range(count):
            # Select random parameters
            soc = self.config["s32ds_socs"][i % len(self.config["s32ds_socs"])]
            package = self.config["packages"][i % len(self.config["packages"])]
            category = self.config["categories"][i % len(self.config["categories"])]
            complexity = self.config["complexity_levels"][i % len(self.config["complexity_levels"])]
            
            # Generate scenario using AI
            scenario = await self.generate_single_scenario(soc, package, category, complexity)
            scenarios.append(scenario)
            
            if (i + 1) % 10 == 0:
                logger.info(f"Generated {i + 1}/{count} scenarios")
        
        return scenarios

    async def generate_single_scenario(self, soc: str, package: str, category: str, complexity: str) -> Dict[str, Any]:
        """Generate a single test scenario using AI"""
        
        prompt = self.build_scenario_generation_prompt(soc, package, category, complexity)
        
        try:
            # Try primary model (GPT-4)
            response = await self.call_openai_api(prompt)
            scenario = self.parse_ai_response(response, soc, package, category, complexity)
        except Exception as e:
            logger.warning(f"Primary model failed: {e}, trying fallback")
            # Fallback to Claude
            response = await self.call_anthropic_api(prompt)
            scenario = self.parse_ai_response(response, soc, package, category, complexity)
        
        return scenario

    def build_scenario_generation_prompt(self, soc: str, package: str, category: str, complexity: str) -> str:
        """Build prompt for AI scenario generation"""
        
        base_prompt = f"""
Generate a detailed S32DS test scenario with the following specifications:

**Target Configuration:**
- SoC: {soc}
- Package: {package}
- Category: {category}
- Complexity: {complexity}

**Requirements:**
1. Create a realistic S32DS workflow test case
2. Include 5-10 detailed test steps
3. Each step should have clear action and expected result
4. Generate corresponding SWTBot code for each step
5. Focus on {category} workflows specific to {soc}

**Output Format (JSON):**
{{
  "name": "Test case name",
  "description": "Detailed description of what this test validates",
  "soc": "{soc}",
  "package": "{package}",
  "category": "{category}",
  "steps": [
    {{
      "step_number": 1,
      "action": "Specific action to perform",
      "expected_result": "Expected outcome",
      "swtbot_code": "bot.menu(\\"File\\").click();"
    }}
  ],
  "complete_swtbot_test": "Complete SWTBot test method code"
}}

**S32DS Context:**
- S32DS is NXP's Eclipse-based IDE for automotive microcontrollers
- {soc} is a {self.get_soc_description(soc)}
- Focus on automotive-specific workflows and configurations
- Include realistic project names and settings

Generate a comprehensive, realistic test scenario:
"""
        return base_prompt

    def get_soc_description(self, soc: str) -> str:
        """Get description for specific SoC"""
        descriptions = {
            "S32K144": "32-bit ARM Cortex-M4F automotive microcontroller with 1MB Flash",
            "S32K146": "32-bit ARM Cortex-M4F automotive microcontroller with 1MB Flash and HSM",
            "S32K148": "32-bit ARM Cortex-M4F automotive microcontroller with 2MB Flash",
            "S32G274A": "32-bit ARM Cortex-A53 automotive processor for gateway applications",
            "S32G399A": "32-bit ARM Cortex-A53 automotive processor with enhanced security"
        }
        return descriptions.get(soc, "automotive microcontroller/processor")

    async def call_openai_api(self, prompt: str) -> str:
        """Call OpenAI API for text generation"""
        # Placeholder for OpenAI API call
        # In real implementation, use openai library
        logger.info("Calling OpenAI API...")
        
        # Simulated response for demo
        return """
        {
          "name": "S32K144 CAN Communication Setup",
          "description": "Test CAN peripheral configuration and message transmission for S32K144",
          "soc": "S32K144",
          "package": "LQFP100",
          "category": "peripheral_config",
          "steps": [
            {
              "step_number": 1,
              "action": "Create new S32K144 project",
              "expected_result": "Project created successfully in workspace",
              "swtbot_code": "bot.menu(\\"File\\").menu(\\"New\\").menu(\\"S32DS Project\\").click();"
            }
          ]
        }
        """

    async def call_anthropic_api(self, prompt: str) -> str:
        """Call Anthropic API for text generation"""
        # Placeholder for Anthropic API call
        logger.info("Calling Anthropic API...")
        return await self.call_openai_api(prompt)  # Fallback simulation

    def parse_ai_response(self, response: str, soc: str, package: str, category: str, complexity: str) -> Dict[str, Any]:
        """Parse AI response into structured format"""
        try:
            # Try to parse as JSON
            data = json.loads(response.strip())
            
            # Validate and enhance the response
            data["soc"] = soc
            data["package"] = package
            data["category"] = category
            data["complexity"] = complexity
            data["metadata"] = {
                "generated_by": "ai_training_data_generator",
                "generation_timestamp": "2025-06-17T12:00:00Z",
                "model_used": "gpt-4-turbo"
            }
            
            return data
            
        except json.JSONDecodeError:
            logger.error("Failed to parse AI response as JSON")
            # Return a fallback structure
            return self.create_fallback_scenario(soc, package, category, complexity)

    def create_fallback_scenario(self, soc: str, package: str, category: str, complexity: str) -> Dict[str, Any]:
        """Create fallback scenario when AI generation fails"""
        return {
            "name": f"{soc} {category.replace('_', ' ').title()} Test",
            "description": f"Basic {category} test for {soc} with {package} package",
            "soc": soc,
            "package": package,
            "category": category,
            "complexity": complexity,
            "steps": [
                {
                    "step_number": 1,
                    "action": f"Create new {soc} project",
                    "expected_result": "Project created successfully",
                    "swtbot_code": 'bot.menu("File").menu("New").menu("S32DS Project").click();'
                }
            ],
            "metadata": {
                "generated_by": "fallback_generator",
                "generation_timestamp": "2025-06-17T12:00:00Z"
            }
        }

    def generate_rag_training_data(self, scenarios: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert scenarios to RAG training format"""
        logger.info("Converting scenarios to RAG training format...")
        
        rag_data = []
        for scenario in scenarios:
            rag_item = {
                "query": f"How to {scenario['description'].lower()}",
                "context": {
                    "soc": scenario["soc"],
                    "package": scenario["package"],
                    "category": scenario["category"],
                    "steps": scenario["steps"]
                },
                "answer": scenario.get("complete_swtbot_test", ""),
                "metadata": {
                    "source": "ai_generated",
                    "complexity": scenario["complexity"],
                    "category": scenario["category"]
                }
            }
            rag_data.append(rag_item)
        
        return rag_data

    def generate_fine_tuning_data(self, scenarios: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert scenarios to fine-tuning format"""
        logger.info("Converting scenarios to fine-tuning format...")
        
        fine_tuning_data = []
        for scenario in scenarios:
            # Create instruction-following format
            instruction = f"Generate SWTBot test code for {scenario['name']}"
            input_text = f"SoC: {scenario['soc']}, Package: {scenario['package']}, Category: {scenario['category']}"
            output_text = scenario.get("complete_swtbot_test", "")
            
            fine_tuning_item = {
                "instruction": instruction,
                "input": input_text,
                "output": output_text,
                "metadata": scenario.get("metadata", {})
            }
            fine_tuning_data.append(fine_tuning_item)
        
        return fine_tuning_data

    def save_training_data(self, data: List[Dict[str, Any]], output_path: str, format_type: str = "json"):
        """Save training data to file"""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        if format_type == "json":
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2)
        elif format_type == "yaml":
            with open(output_file, 'w') as f:
                yaml.dump(data, f, default_flow_style=False)
        
        logger.info(f"Saved {len(data)} training examples to {output_file}")

async def main():
    """Main function to run the training data generator"""
    parser = argparse.ArgumentParser(description="Generate S32DS training data using AI")
    parser.add_argument("--count", type=int, default=100, help="Number of scenarios to generate")
    parser.add_argument("--output-dir", default="generated_training_data", help="Output directory")
    parser.add_argument("--format", choices=["json", "yaml"], default="json", help="Output format")
    
    args = parser.parse_args()
    
    # Initialize generator
    generator = TrainingDataGenerator()
    
    # Generate scenarios
    scenarios = await generator.generate_s32ds_test_scenarios(args.count)
    
    # Convert to different training formats
    rag_data = generator.generate_rag_training_data(scenarios)
    fine_tuning_data = generator.generate_fine_tuning_data(scenarios)
    
    # Save data
    output_dir = Path(args.output_dir)
    generator.save_training_data(scenarios, output_dir / f"raw_scenarios.{args.format}", args.format)
    generator.save_training_data(rag_data, output_dir / f"rag_training_data.{args.format}", args.format)
    generator.save_training_data(fine_tuning_data, output_dir / f"fine_tuning_data.{args.format}", args.format)
    
    logger.info(f"Training data generation complete! Generated {len(scenarios)} scenarios.")
    logger.info(f"Output saved to: {output_dir}")

if __name__ == "__main__":
    asyncio.run(main())
