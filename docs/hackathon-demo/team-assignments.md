# 👥 Team Assignments for Hackathon Demo

## 🎯 Team Overview

**4-Person Team Structure** for 2-week sprint to build S32K144 AI CodeGen demo

## 📋 Jira Epic and Tasks

**Epic**: [AIC-2 - FPT-Nvidia Hackathon Demo: S32DS AI CodeGen](https://ai-codegen.atlassian.net/browse/AIC-2)

### **Created Jira Tasks:**
- **[AIC-3]** - Setup FPT AI Factory and Nvidia Environment
- **[AIC-4]** - Create SoC Workflow Database and Prompt Templates
- **[AIC-5]** - S32DS Environment Setup and Multi-SoC Analysis
- **[AIC-6]** - Implement Generic Data-Driven SWTBot Tests
- **[AIC-7]** - Maven Data-Driven Test Framework and Integration
- **[AIC-8]** - Demo Preparation and Presentation Materials
- **[AIC-9]** - System Integration and End-to-End Testing

**📊 Task Summary**: 7 detailed tasks under 1 Epic, all set to High priority with comprehensive acceptance criteria.

## � Jira Epic and Tasks

**Epic**: [AIC-2 - FPT-Nvidia Hackathon Demo: S32DS AI CodeGen](https://your-jira-instance.atlassian.net/browse/AIC-2)

### **Created Jira Tasks:**
- **[AIC-3]** - Setup FPT AI Factory and Nvidia Environment
- **[AIC-4]** - Create SoC Workflow Database and Prompt Templates
- **[AIC-5]** - S32DS Environment Setup and Multi-SoC Analysis
- **[AIC-6]** - Implement Generic Data-Driven SWTBot Tests
- **[AIC-7]** - Maven Data-Driven Test Framework and Integration
- **[AIC-8]** - Demo Preparation and Presentation Materials
- **[AIC-9]** - System Integration and End-to-End Testing

## �👤 Person 1: AI/ML Engineer

### **Primary Responsibilities**
- RAG system implementation with **FPT AI Factory**
- Local LLM integration with **Nvidia optimizations**
- S32K144-specific code generation pipeline
- Training data preparation with file name parameters

### **Week 1 Tasks**
#### **Days 1-2: Foundation Setup**
- [ ] **FPT AI Factory Setup** (4 hours)
  - Obtain API credentials and access tokens
  - Configure development environment for FPT AI Factory
  - Test connection and basic API calls
  - Document authentication process
- [ ] **Nvidia Environment Configuration** (3 hours)
  - Install CUDA toolkit and TensorRT
  - Configure GPU acceleration for inference
  - Test Nvidia NIM services (if available)
  - Verify GPU memory and performance
- [ ] **Local LLM Backup Setup** (2 hours)
  - Install Ollama with CodeLlama-7B model
  - Configure local inference environment
  - Test model response times and quality
- [ ] **RAG System Foundation** (3 hours)
  - Set up vector database with GPU acceleration
  - Implement basic embedding generation
  - Create simple similarity search functionality
  - Test with sample S32DS queries

#### **Days 3-4: Training Data & Patterns**
- [ ] **SoC Workflow Database Creation** (4 hours)
  - Document S32K144 project creation workflow (step-by-step)
  - Create S32K146 and S32G274A workflow variations
  - Define file naming conventions and parameters
  - Build structured workflow database (JSON/CSV)
- [ ] **Prompt Engineering** (3 hours)
  - Design prompt templates for code generation
  - Create SoC-agnostic prompt structures
  - Implement parameter substitution patterns
  - Test prompts with FPT AI Factory models
- [ ] **Intent Recognition System** (4 hours)
  - Build intent classification for user requests
  - Map intents to SoC workflows and configurations
  - Implement confidence scoring for intent matching
  - Create fallback mechanisms for unclear intents
- [ ] **Pattern Validation** (1 hour)
  - Test pattern matching with sample user inputs
  - Validate file name parameter extraction
  - Verify SoC configuration mapping accuracy

#### **Days 5-7: Code Generation Pipeline**
- [ ] Build general template-based code generation system with SoC parameterization
- [ ] Integrate RAG with LLM for SoC code generation using **Nvidia acceleration**
- [ ] Implement parameter substitution with file names and SoC configurations (S32K144 example)
- [ ] Create validation for generated SWTBot code with general function names

### **Week 2 Tasks**
#### **Days 8-9: Integration & Testing**
- [ ] Integrate AI system with Maven project generator
- [ ] Test end-to-end code generation pipeline
- [ ] Fine-tune prompts for better code quality
- [ ] Implement error handling for edge cases

#### **Days 10-11: Demo Preparation**
- [ ] Optimize response times for live demo
- [ ] Create demo-specific prompts and responses
- [ ] Prepare fallback responses for demo scenarios
- [ ] Test AI components with demo scripts

#### **Days 12-14: Final Polish**
- [ ] Performance optimization and caching
- [ ] Demo rehearsals and AI component testing
- [ ] Prepare technical explanations for presentation
- [ ] Create backup AI responses for demo

### **Deliverables**
- Working RAG + LLM system
- Code generation pipeline
- Demo-ready AI components
- Technical documentation

---

## 👤 Person 2: S32DS/SWTBot Expert

### **Primary Responsibilities**
- S32DS workflow analysis
- SWTBot test implementation
- S32K144 specific configurations
- IDE integration testing

### **Week 1 Tasks**
#### **Days 1-2: Environment & Analysis**
- [ ] **S32DS Installation & Configuration** (3 hours)
  - Install latest S32DS with S32K1 and S32G SDKs
  - Configure workspace and perspectives
  - Verify S32K144, S32K146, S32G274A support
  - Document installation process and requirements
- [ ] **Multi-SoC Workflow Analysis** (4 hours)
  - Map S32K144 project creation workflow (UI elements, timing)
  - Document S32K146 and S32G274A differences
  - Identify common UI patterns across SoCs
  - Create workflow comparison matrix
- [ ] **SWTBot Environment Setup** (1 hour)
  - Configure SWTBot testing environment
  - Set up test workspace and perspectives
  - Verify SWTBot can interact with S32DS UI elements

#### **Days 3-4: Project Creation SWTBot**
- [ ] Implement S32K144 project creation SWTBot script
- [ ] Test project creation with different configurations
- [ ] Handle LQFP100 package selection
- [ ] Add proper wait conditions and error handling

#### **Days 5-7: Build Execution SWTBot**
- [ ] Implement build execution SWTBot script
- [ ] Test build process automation
- [ ] Add build verification and success checking
- [ ] Handle build errors and timeouts

### **Week 2 Tasks**
#### **Days 8-9: Integration Testing**
- [ ] Test SWTBot scripts with generated Maven projects
- [ ] Verify compatibility with AI-generated code
- [ ] Fix integration issues and timing problems
- [ ] Optimize script reliability and performance

#### **Days 10-11: Demo Scenarios**
- [ ] Create multiple demo scenarios for testing
- [ ] Test scripts with different S32K144 configurations
- [ ] Prepare demo environment and clean workspace
- [ ] Create backup test data and projects

#### **Days 12-14: Demo Preparation**
- [ ] Final testing with complete demo flow
- [ ] Prepare S32DS environment for live demo
- [ ] Create contingency plans for demo failures
- [ ] Practice live demo execution

### **Deliverables**
- S32K144 project creation SWTBot tests
- Build execution SWTBot tests
- Demo-ready S32DS environment
- SWTBot troubleshooting guide

---

## 👤 Person 3: Backend/Integration Developer

### **Primary Responsibilities**
- Maven project templates
- System integration
- API development
- Testing framework setup

### **Week 1 Tasks**
#### **Days 1-2: Maven Foundation**
- [ ] Create Maven project template structure
- [ ] Set up SWTBot dependencies and configuration
- [ ] Create base test classes and utilities
- [ ] Set up build and execution scripts

#### **Days 3-4: Integration Framework**
- [ ] Build bridge between AI generator and Maven projects
- [ ] Implement template parameter substitution
- [ ] Create project generation pipeline
- [ ] Set up automated testing framework

#### **Days 5-7: API Development**
- [ ] Create simple API for code generation requests
- [ ] Implement file I/O for input/output handling
- [ ] Build command-line interface
- [ ] Add logging and debugging capabilities

### **Week 2 Tasks**
#### **Days 8-9: System Integration**
- [ ] Integrate all components (AI + SWTBot + Maven)
- [ ] Test end-to-end system functionality
- [ ] Fix integration bugs and performance issues
- [ ] Implement error handling and recovery

#### **Days 10-11: CLI and User Experience**
- [ ] Polish command-line interface
- [ ] Add input validation and user feedback
- [ ] Create user-friendly output formatting
- [ ] Implement demo-specific features

#### **Days 12-14: Testing & Deployment**
- [ ] Comprehensive system testing
- [ ] Performance optimization
- [ ] Deployment preparation and packaging
- [ ] Create system documentation

### **Deliverables**
- Maven project generation system
- Integration API and CLI
- End-to-end testing framework
- Deployment package

---

## 👤 Person 4: Frontend/Demo Lead

### **Primary Responsibilities**
- Demo presentation preparation
- User interface development
- Live demo coordination
- Documentation and presentation

### **Week 1 Tasks**
#### **Days 1-2: Demo Planning**
- [ ] Define demo scenarios and user stories
- [ ] Create presentation outline and structure
- [ ] Design demo flow and timing
- [ ] Set up documentation framework

#### **Days 3-4: Content Creation**
- [ ] Create sample inputs and expected outputs
- [ ] Develop demo scripts and talking points
- [ ] Design presentation slides and visuals
- [ ] Document system capabilities and limitations

#### **Days 5-7: User Experience**
- [ ] Design simple user interface (if needed)
- [ ] Create user guides and documentation
- [ ] Develop demo materials and handouts
- [ ] Plan audience interaction and Q&A

### **Week 2 Tasks**
#### **Days 8-9: Demo Interface**
- [ ] Build simple demo interface or improve CLI
- [ ] Create visual feedback for code generation
- [ ] Design demo environment and setup
- [ ] Test user experience flow

#### **Days 10-11: Presentation Preparation**
- [ ] Finalize presentation slides and materials
- [ ] Create demo videos as backup
- [ ] Prepare technical explanations and talking points
- [ ] Design booth setup and demo environment

#### **Days 12-14: Demo Rehearsals**
- [ ] Conduct full demo rehearsals
- [ ] Train team on presentation delivery
- [ ] Prepare for Q&A and technical questions
- [ ] Finalize demo day logistics

### **Deliverables**
- Complete presentation materials
- Demo interface and user experience
- Documentation and user guides
- Demo day coordination plan

---

## 🤝 Cross-Team Collaboration

### **Daily Standups (15 minutes)**
- Progress updates from each team member
- Blocker identification and resolution
- Task coordination and dependencies
- Risk assessment and mitigation

### **Integration Points**
- **Day 4**: AI + SWTBot integration test
- **Day 7**: End-of-week integration review
- **Day 9**: Full system integration test
- **Day 11**: Demo rehearsal with all components
- **Day 13**: Final integration and demo practice

### **Communication Channels**
- **Daily**: Slack/Teams for quick updates
- **Weekly**: Video calls for detailed reviews
- **Critical**: Immediate escalation for blockers

### **Shared Deliverables**
- [ ] Integrated demo system
- [ ] Complete presentation
- [ ] Technical documentation
- [ ] Demo day execution plan

## 📊 Success Metrics

### **Individual Success**
- [ ] All assigned tasks completed on time
- [ ] Components integrate successfully
- [ ] Demo responsibilities executed flawlessly
- [ ] Technical knowledge shared effectively

### **Team Success**
- [ ] Working end-to-end demo system
- [ ] Successful live demonstration
- [ ] Positive audience feedback
- [ ] Technical goals achieved

Ready to start the sprint! 🚀
