# 📋 Structured Test Case Format for S32DS Training Data

## 📋 Overview

This document defines the structured test case format used for training the local LLM models with S32DS-specific knowledge. The format ensures consistent, high-quality training data with clear test steps and expected results for each S32DS workflow, supporting various SoCs including S32K144 as examples.

## 🎯 Test Case Structure

### **Structured Format with Separated Steps and Expected Outputs**
Every test case must include:
1. **Test Step**: Clear, actionable instruction (separate function)
2. **Expected Result**: Specific, verifiable outcome (included in training data)
3. **Input Data**: Test parameters and configuration
4. **Expected Output**: Generated SWTBot code for validation

### **Enhanced CSV Input Format**
```csv
test_case_id,test_case_name,soc,package,test_step,expected_result,step_order,category,step_function_name,expected_swtbot_code
TC001,Create Project,S32K144,LQFP100,Open S32DS IDE,S32DS IDE launches successfully with welcome screen,1,project_creation,openS32DSIde,"bot.menu(""File"").isEnabled()",
TC001,Create Project,S32K144,LQ<PERSON>100,Navigate to File > New > S32DS Project,New S32DS Project wizard opens,2,project_creation,openNewProjectWizard,"bot.menu(""File"").menu(""New"").menu(""S32DS Project"").click()",
TC001,Create Project,S32K144,LQFP100,Select S32K1 SDK project type,S32K1 SDK option is highlighted and selectable,3,project_creation,selectSDKType,"bot.tree().select(""S32K1 SDK"")",
TC001,Create Project,S32K144,LQFP100,Enter project name TestProject,Project name field shows TestProject,4,project_creation,enterProjectName,"bot.textWithLabel(""Project name:"").setText(projectName)",
TC001,Create Project,S32K144,LQFP100,Configure target processor,Processor dropdown shows selected SoC,5,project_creation,configureTargetProcessor,"bot.comboBoxWithLabel(""Processor:"").setSelection(soc)",
TC001,Create Project,S32K144,LQFP100,Select package type,Package field displays selected package,6,project_creation,selectPackageType,"bot.comboBoxWithLabel(""Package:"").setSelection(packageType)",
TC001,Create Project,S32K144,LQFP100,Click Finish,Project creation progress dialog appears,7,project_creation,finishProjectCreation,"bot.button(""Finish"").click()",
TC001,Create Project,S32K144,LQFP100,Wait for project creation,Project appears in Project Explorer tree,8,project_creation,waitForProjectCreation,"bot.waitUntil(treeHasItems())",
TC001,Create Project,S32K144,LQFP100,Verify project configuration,Project properties show correct processor settings,9,project_creation,verifyProjectConfiguration,"assertTrue(""Project should be configured correctly"")"
```

## 🔧 S32DS Knowledge Base Categories

### **1. Project Management**
```csv
test_case_id,test_case_name,soc,package,test_step,expected_result,step_order,category,step_function_name,expected_swtbot_code
TC002,Import Project,S32K144,LQFP100,Open File > Import menu,Import dialog opens,1,project_import,openImportMenu,"bot.menu(""File"").menu(""Import"").click()",
TC002,Import Project,S32K144,LQFP100,Select S32DS > Existing S32DS Project,Import wizard shows S32DS project options,2,project_import,selectImportType,"bot.tree().select(""S32DS"").select(""Existing S32DS Project"")",
TC002,Import Project,S32K144,LQFP100,Browse to project folder,File browser opens to select project,3,project_import,browseToProjectFolder,"bot.button(""Browse..."").click()",
TC002,Import Project,S32K144,LQFP100,Select project and click OK,Project appears in import list,4,project_import,selectProjectToImport,"bot.tree().select(projectPath)",
TC002,Import Project,S32K144,LQFP100,Click Finish,Project imports successfully,5,project_import,finishProjectImport,"bot.button(""Finish"").click()",
TC002,Import Project,S32K144,LQFP100,Verify project in explorer,Project visible with correct configuration,6,project_import,verifyProjectImported,"assertTrue(""Project should be visible"")"
```

### **2. Build Configuration**
```csv
test_case_id,test_case_name,soc,package,test_step,expected_result,step_order,category,step_function_name,expected_swtbot_code
TC003,Configure Build,S32K144,LQFP100,Right-click project > Properties,Project Properties dialog opens,1,build_config,openProjectProperties,"bot.tree().select(projectName).contextMenu(""Properties"").click()",
TC003,Configure Build,S32K144,LQFP100,Navigate to C/C++ Build > Settings,Build settings page displays,2,build_config,navigateToBuildSettings,"bot.tree().select(""C/C++ Build"").select(""Settings"")",
TC003,Configure Build,S32K144,LQFP100,Select S32DS ARM Toolchain,Toolchain shows S32DS ARM selected,3,build_config,selectToolchain,"bot.comboBoxWithLabel(""Toolchain:"").setSelection(""S32DS ARM"")",
TC003,Configure Build,S32K144,LQFP100,Configure for target processor,Target processor shows selected SoC,4,build_config,configureTargetProcessor,"bot.comboBoxWithLabel(""Target Processor:"").setSelection(soc)",
TC003,Configure Build,S32K144,LQFP100,Set optimization level -O2,Optimization field shows -O2,5,build_config,setOptimizationLevel,"bot.comboBoxWithLabel(""Optimization Level:"").setSelection(""-O2"")",
TC003,Configure Build,S32K144,LQFP100,Apply and Close,Settings saved successfully,6,build_config,applyBuildSettings,"bot.button(""Apply and Close"").click()"
```

### **3. Debug Configuration**
```csv
test_case_id,test_case_name,soc,package,test_step,expected_result,step_order,category,step_function_name,expected_swtbot_code
TC004,Setup Debug,S32K144,LQFP100,Open Run > Debug Configurations,Debug Configurations dialog opens,1,debug_config,openDebugConfigurations,"bot.menu(""Run"").menu(""Debug Configurations"").click()",
TC004,Setup Debug,S32K144,LQFP100,Create new S32DS Debugger config,New debug configuration created,2,debug_config,createDebugConfig,"bot.tree().select(""S32DS Debugger"").contextMenu(""New"").click()",
TC004,Setup Debug,S32K144,LQFP100,Set project to target project,Project field shows selected project,3,debug_config,setDebugProject,"bot.textWithLabel(""Project:"").setText(projectName)",
TC004,Setup Debug,S32K144,LQFP100,Select PE micro Multilink probe,Debug probe shows PE micro Multilink,4,debug_config,selectDebugProbe,"bot.comboBoxWithLabel(""Debug Probe:"").setSelection(""PE micro Multilink"")",
TC004,Setup Debug,S32K144,LQFP100,Configure JTAG interface,Interface field shows JTAG,5,debug_config,configureInterface,"bot.comboBoxWithLabel(""Interface:"").setSelection(""JTAG"")",
TC004,Setup Debug,S32K144,LQFP100,Apply configuration,Debug config saved successfully,6,debug_config,applyDebugConfig,"bot.button(""Apply"").click()"
```

### **4. Peripheral Configuration**
```csv
test_case_id,test_case_name,soc,package,test_step,expected_result,step_order,category,step_function_name,expected_swtbot_code
TC005,Configure GPIO,S32K144,LQFP100,Open pinmux.mex file,Pin muxing tool opens,1,peripheral_config,openPinMuxTool,"bot.tree().select(""pinmux.mex"").doubleClick()",
TC005,Configure GPIO,S32K144,LQFP100,Select pin PTB0,Pin PTB0 highlighted in pin view,2,peripheral_config,selectPin,"bot.table().cell(0, ""Pin Name"").click()",
TC005,Configure GPIO,S32K144,LQFP100,Set function to GPIO,Function dropdown shows GPIO,3,peripheral_config,setPinFunction,"bot.comboBoxWithLabel(""Function:"").setSelection(""GPIO"")",
TC005,Configure GPIO,S32K144,LQFP100,Set direction to Output,Direction field shows Output,4,peripheral_config,setPinDirection,"bot.comboBoxWithLabel(""Direction:"").setSelection(""Output"")",
TC005,Configure GPIO,S32K144,LQFP100,Set initial value High,Initial value shows High,5,peripheral_config,setPinInitialValue,"bot.comboBoxWithLabel(""Initial Value:"").setSelection(""High"")",
TC005,Configure GPIO,S32K144,LQFP100,Generate code,Code generation completes successfully,6,peripheral_config,generateCode,"bot.button(""Generate Code"").click()"
```

## 🧠 Enhanced Training Data Processing Pipeline

### **1. CSV to JSON Conversion with Separated Steps**
```python
def convert_csv_to_training_format(csv_file):
    """Convert structured CSV to training JSON format with separated test steps"""
    df = pd.read_csv(csv_file)
    training_data = []

    # Group by test case ID
    for test_case_id, group in df.groupby('test_case_id'):
        # Main test case with expected output
        test_case = {
            "instruction": f"Generate SWTBot test for {group.iloc[0]['test_case_name']} on {group.iloc[0]['soc']}",
            "input": {
                "test_case": group.iloc[0]['test_case_name'],
                "soc": group.iloc[0]['soc'],
                "package": group.iloc[0]['package'],
                "test_steps": [],
                "step_functions": []
            },
            "output": {
                "main_test_function": generate_main_test_function(group),
                "step_functions": generate_step_functions(group),
                "expected_swtbot_code": generate_complete_swtbot_code(group)
            },
            "metadata": {
                "category": group.iloc[0]['category'],
                "soc": group.iloc[0]['soc'],
                "package": group.iloc[0]['package'],
                "step_count": len(group),
                "complexity": calculate_complexity(group)
            }
        }

        # Add test steps with expected SWTBot code
        for _, row in group.iterrows():
            test_case["input"]["test_steps"].append({
                "step": row['test_step'],
                "expected_result": row['expected_result'],
                "step_order": row['step_order']
            })
            test_case["input"]["step_functions"].append({
                "function_name": row['step_function_name'],
                "expected_code": row['expected_swtbot_code'],
                "step_order": row['step_order']
            })

        training_data.append(test_case)

    return training_data
```

### **2. Separated SWTBot Code Generation**
```python
def generate_main_test_function(test_steps_group):
    """Generate main test function that calls individual step functions"""
    test_name = test_steps_group.iloc[0]['test_case_name'].replace(' ', '')
    soc = test_steps_group.iloc[0]['soc']
    package = test_steps_group.iloc[0]['package']

    code_lines = [
        "@Test",
        f"public void test{test_name}() {{",
        f"    // Test configuration",
        f"    String soc = \"{soc}\";",
        f"    String packageType = \"{package}\";",
        f"    String projectName = \"TestProject_\" + soc;",
        ""
    ]

    # Call each step function
    for _, step in test_steps_group.iterrows():
        function_name = step['step_function_name']
        code_lines.append(f"    {function_name}(soc, packageType, projectName);")

    code_lines.append("}")
    return "\n".join(code_lines)

def generate_step_functions(test_steps_group):
    """Generate individual step functions"""
    functions = []

    for _, step in test_steps_group.iterrows():
        function_code = [
            f"private void {step['step_function_name']}(String soc, String packageType, String projectName) {{",
            f"    // Test Step: {step['test_step']}",
            f"    {step['expected_swtbot_code']}",
            f"    // Expected Result: {step['expected_result']}",
            f"    // Add validation here",
            "}"
        ]
        functions.append("\n".join(function_code))

    return functions

def generate_complete_swtbot_code(test_steps_group):
    """Generate complete SWTBot test class with all functions"""
    main_function = generate_main_test_function(test_steps_group)
    step_functions = generate_step_functions(test_steps_group)

    return {
        "main_test": main_function,
        "step_functions": step_functions,
        "complete_class": main_function + "\n\n" + "\n\n".join(step_functions)
    }
```

## 📊 S32K144-Specific Knowledge Base

### **SoC-Specific Information**
```json
{
  "s32k144_specs": {
    "core": "ARM Cortex-M4F",
    "frequency": "112 MHz",
    "flash": "1 MB",
    "ram": "128 KB",
    "packages": ["LQFP100", "LQFP144", "MAPBGA100"],
    "peripherals": {
      "flexcan": 3,
      "uart": 3,
      "spi": 3,
      "i2c": 2,
      "adc": 2,
      "flextimer": 8
    },
    "gpio_ports": ["PTA", "PTB", "PTC", "PTD", "PTE"],
    "pin_count": {
      "LQFP100": 100,
      "LQFP144": 144,
      "MAPBGA100": 100
    }
  }
}
```

### **S32DS UI Element Mappings for S32K144**
```json
{
  "s32k144_ui_mappings": {
    "project_wizard": {
      "sdk_selection": "S32K1 SDK",
      "processor_dropdown": "S32K144",
      "package_options": ["LQFP100", "LQFP144", "MAPBGA100"],
      "sdk_versions": ["S32K1_SDK_3.0.0", "S32K1_SDK_2.0.3"]
    },
    "build_settings": {
      "toolchain": "S32DS ARM",
      "target_processor": "S32K144",
      "optimization_levels": ["-O0", "-O1", "-O2", "-O3", "-Os"]
    },
    "debug_config": {
      "debug_probes": ["PE micro Multilink", "SEGGER J-Link", "OpenSDA"],
      "interfaces": ["JTAG", "SWD"],
      "target_device": "S32K144"
    }
  }
}
```

## 🎯 Quality Validation

### **Test Case Quality Metrics**
```python
QUALITY_METRICS = {
    "step_clarity": 0.25,           # Clear, actionable test steps
    "result_specificity": 0.25,     # Specific, verifiable expected results
    "s32k144_accuracy": 0.20,       # Correct S32K144-specific information
    "ui_element_precision": 0.15,   # Accurate S32DS UI element references
    "workflow_completeness": 0.15   # Complete end-to-end workflow coverage
}
```

### **Validation Checklist**
- [ ] Each test step is clear and actionable
- [ ] Expected results are specific and verifiable
- [ ] S32K144 specifications are accurate
- [ ] UI element names match S32DS interface
- [ ] Package-specific information is correct
- [ ] Workflow covers complete user scenario
- [ ] SWTBot code syntax is valid
- [ ] Assertions match expected results

---

**Document Status**: Final v1.0  
**Last Updated**: 2025-06-15  
**Target SoC**: S32K144  
**Review Schedule**: Weekly during data collection phase
