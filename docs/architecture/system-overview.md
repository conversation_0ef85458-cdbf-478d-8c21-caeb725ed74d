# 🏗️ System Architecture Overview - AI CodeGen RAG System

## 📋 Executive Summary

This document provides a comprehensive overview of the AI CodeGen system architecture, designed to generate SWTBot test code using a Retrieval-Augmented Generation (RAG) approach combined with Large Language Models (LLMs). The system transforms natural language test descriptions into fully functional SWTBot test suites.

## 🎯 Architecture Principles

### **1. Modularity**
- **Loosely Coupled Components**: Each component can be developed, tested, and deployed independently
- **Clear Interfaces**: Well-defined APIs between components
- **Pluggable Architecture**: Easy to swap implementations (e.g., different LLM providers)

### **2. Scalability**
- **Horizontal Scaling**: Components can scale independently based on load
- **Async Processing**: Non-blocking operations for better throughput
- **Caching Strategy**: Multi-level caching to reduce latency

### **3. Reliability**
- **Fault Tolerance**: Graceful degradation when components fail
- **Circuit Breakers**: Prevent cascade failures
- **Health Monitoring**: Comprehensive monitoring and alerting

### **4. Maintainability**
- **Clean Code**: Following Python best practices and patterns
- **Comprehensive Testing**: Unit, integration, and end-to-end tests
- **Documentation**: Self-documenting code and comprehensive docs

## 🏛️ High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        CLI[CLI Tool]
        WEB[Web Interface]
        IDE[IDE Plugin]
        API_CLIENT[API Client]
    end
    
    subgraph "API Gateway"
        GATEWAY[FastAPI Gateway]
        AUTH[Authentication]
        RATE[Rate Limiting]
        LOAD[Load Balancer]
    end
    
    subgraph "Core Services"
        ORCHESTRATOR[Request Orchestrator]
        RAG[RAG Service]
        LLM[LLM Service]
        VALIDATOR[Code Validator]
        GENERATOR[Project Generator]
    end
    
    subgraph "Data Services"
        VECTOR[Vector Database]
        METADATA[Metadata Database]
        CACHE[Cache Layer]
        STORAGE[File Storage]
    end
    
    subgraph "Infrastructure"
        MONITOR[Monitoring]
        LOGGING[Logging]
        CONFIG[Configuration]
        SECURITY[Security]
    end
    
    CLI --> GATEWAY
    WEB --> GATEWAY
    IDE --> GATEWAY
    API_CLIENT --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> RATE
    GATEWAY --> LOAD
    
    LOAD --> ORCHESTRATOR
    ORCHESTRATOR --> RAG
    ORCHESTRATOR --> LLM
    ORCHESTRATOR --> VALIDATOR
    ORCHESTRATOR --> GENERATOR
    
    RAG --> VECTOR
    RAG --> CACHE
    LLM --> CACHE
    VALIDATOR --> METADATA
    GENERATOR --> STORAGE
    
    ORCHESTRATOR --> MONITOR
    ORCHESTRATOR --> LOGGING
```

## 🔧 Component Architecture

### **1. API Gateway Layer**

#### **FastAPI Gateway**
```python
# Core responsibilities
GATEWAY_RESPONSIBILITIES = [
    "Request routing and load balancing",
    "Authentication and authorization",
    "Rate limiting and throttling",
    "Request/response validation",
    "API documentation (OpenAPI/Swagger)",
    "CORS handling",
    "Error handling and standardization"
]
```

#### **Authentication Service**
```python
# JWT-based authentication
AUTH_FEATURES = [
    "User registration and login",
    "JWT token generation and validation",
    "Role-based access control (RBAC)",
    "API key management",
    "Session management",
    "Password reset functionality"
]
```

### **2. Core Processing Services**

#### **Request Orchestrator**
```python
class RequestOrchestrator:
    """
    Central coordinator for processing code generation requests
    """
    async def process_request(self, request: CodeGenRequest) -> CodeGenResponse:
        # 1. Validate and preprocess request
        processed_request = await self.preprocess(request)
        
        # 2. Retrieve relevant patterns via RAG
        context = await self.rag_service.retrieve_context(processed_request)
        
        # 3. Generate code using LLM
        generated_code = await self.llm_service.generate_code(
            processed_request, context
        )
        
        # 4. Validate and optimize generated code
        validated_code = await self.validator.validate(generated_code)
        
        # 5. Generate complete project structure
        project = await self.generator.create_project(validated_code)
        
        return CodeGenResponse(project=project, metadata=metadata)
```

#### **RAG Service**
```python
class RAGService:
    """
    Retrieval-Augmented Generation service for pattern retrieval
    """
    def __init__(self):
        self.vector_db = ChromaDBClient()
        self.embedder = SentenceTransformer('all-MiniLM-L6-v2')
        self.ranker = PatternRanker()
    
    async def retrieve_context(self, request: ProcessedRequest) -> Context:
        # Embed the query
        query_embedding = self.embedder.encode(request.description)
        
        # Retrieve similar patterns
        candidates = await self.vector_db.similarity_search(
            query_embedding, k=20
        )
        
        # Rank and filter patterns
        ranked_patterns = self.ranker.rank(candidates, request)
        
        # Assemble context
        return self.assemble_context(ranked_patterns[:5])
```

#### **LLM Service**
```python
class LLMService:
    """
    Large Language Model integration service
    """
    def __init__(self):
        self.primary_llm = OpenAIClient("gpt-4-turbo")
        self.fallback_llm = AnthropicClient("claude-3-sonnet")
        self.prompt_builder = PromptBuilder()
    
    async def generate_code(self, request: ProcessedRequest, context: Context) -> str:
        # Build optimized prompt
        prompt = self.prompt_builder.build_prompt(request, context)
        
        try:
            # Try primary LLM
            return await self.primary_llm.generate(prompt)
        except Exception as e:
            # Fallback to secondary LLM
            logger.warning(f"Primary LLM failed: {e}, using fallback")
            return await self.fallback_llm.generate(prompt)
```

### **3. Data Layer Architecture**

#### **Vector Database (ChromaDB)**
```python
VECTOR_DB_SCHEMA = {
    "collections": {
        "swtbot_patterns": {
            "embedding_dimension": 768,
            "metadata_fields": [
                "pattern_type", "ui_elements", "complexity",
                "quality_score", "usage_count", "last_updated"
            ]
        },
        "best_practices": {
            "embedding_dimension": 768,
            "metadata_fields": [
                "category", "importance", "applicability"
            ]
        }
    }
}
```

#### **Metadata Database (PostgreSQL)**
```sql
-- Core tables for system metadata
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE generation_requests (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    input_description TEXT NOT NULL,
    generated_code TEXT,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

CREATE TABLE pattern_usage (
    id UUID PRIMARY KEY,
    pattern_id VARCHAR(100) NOT NULL,
    request_id UUID REFERENCES generation_requests(id),
    relevance_score FLOAT,
    used_in_generation BOOLEAN DEFAULT FALSE
);
```

#### **Caching Strategy**
```python
CACHE_LAYERS = {
    "L1_Memory": {
        "type": "In-process LRU cache",
        "size": "100MB",
        "ttl": "5 minutes",
        "use_case": "Frequently accessed patterns"
    },
    "L2_Redis": {
        "type": "Distributed cache",
        "size": "1GB",
        "ttl": "1 hour",
        "use_case": "Generated code, embeddings"
    },
    "L3_Database": {
        "type": "Persistent storage",
        "size": "Unlimited",
        "ttl": "Permanent",
        "use_case": "All patterns and metadata"
    }
}
```

## 🔄 Data Flow Architecture

### **1. Request Processing Flow**

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant Orchestrator
    participant RAG
    participant LLM
    participant Validator
    participant Generator
    
    Client->>Gateway: POST /generate
    Gateway->>Gateway: Authenticate & Validate
    Gateway->>Orchestrator: Forward Request
    
    Orchestrator->>RAG: Retrieve Context
    RAG->>RAG: Embed Query
    RAG->>RAG: Search Patterns
    RAG->>Orchestrator: Return Context
    
    Orchestrator->>LLM: Generate Code
    LLM->>LLM: Build Prompt
    LLM->>LLM: Call LLM API
    LLM->>Orchestrator: Return Generated Code
    
    Orchestrator->>Validator: Validate Code
    Validator->>Validator: Syntax Check
    Validator->>Validator: Quality Check
    Validator->>Orchestrator: Return Validated Code
    
    Orchestrator->>Generator: Create Project
    Generator->>Generator: Generate Maven Structure
    Generator->>Generator: Create Test Files
    Generator->>Orchestrator: Return Project
    
    Orchestrator->>Gateway: Return Response
    Gateway->>Client: Return Generated Project
```

### **2. Pattern Retrieval Flow**

```mermaid
graph LR
    A[User Query] --> B[Query Preprocessing]
    B --> C[Intent Classification]
    C --> D[Entity Extraction]
    D --> E[Query Embedding]
    E --> F[Vector Search]
    F --> G[Metadata Filtering]
    G --> H[Relevance Ranking]
    H --> I[Context Assembly]
    I --> J[Return Context]
```

## 📊 Performance Characteristics

### **1. Latency Targets**
```python
PERFORMANCE_TARGETS = {
    "api_response_time": "< 2 seconds (95th percentile)",
    "rag_retrieval_time": "< 500ms",
    "llm_generation_time": "< 10 seconds",
    "code_validation_time": "< 1 second",
    "project_generation_time": "< 2 seconds"
}
```

### **2. Throughput Targets**
```python
THROUGHPUT_TARGETS = {
    "concurrent_requests": "100 requests/second",
    "daily_generations": "10,000 code generations",
    "pattern_searches": "1,000 searches/second",
    "cache_hit_rate": "> 80%"
}
```

### **3. Scalability Metrics**
```python
SCALABILITY_METRICS = {
    "horizontal_scaling": "Linear scaling up to 10 instances",
    "database_scaling": "Read replicas for query distribution",
    "cache_scaling": "Redis cluster for distributed caching",
    "storage_scaling": "Object storage for generated projects"
}
```

## 🛡️ Security Architecture

### **1. Authentication & Authorization**
- **JWT Tokens**: Stateless authentication with configurable expiration
- **API Keys**: For programmatic access and integrations
- **Role-Based Access**: Different permission levels for users
- **Rate Limiting**: Prevent abuse and ensure fair usage

### **2. Data Protection**
- **Encryption at Rest**: Database and file storage encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Input Validation**: Comprehensive input sanitization
- **Output Sanitization**: Prevent code injection in generated output

### **3. Infrastructure Security**
- **Container Security**: Minimal base images and security scanning
- **Network Security**: VPC isolation and firewall rules
- **Secrets Management**: Secure storage of API keys and credentials
- **Audit Logging**: Comprehensive audit trail for all operations

---

**Document Status**: Final v1.0  
**Last Updated**: 2025-06-15  
**Architecture Review Date**: Bi-weekly
