# 🤖 AI CodeGen for NXP S32DS Testing

<div align="center">
  <img src="https://img.shields.io/badge/AI-Powered-blue?style=for-the-badge&logo=artificial-intelligence" alt="AI Powered">
  <img src="https://img.shields.io/badge/NXP-S32K144-orange?style=for-the-badge&logo=nxp" alt="NXP S32K144">
  <img src="https://img.shields.io/badge/Eclipse-SWTBot-green?style=for-the-badge&logo=eclipse" alt="Eclipse SWTBot">
  <img src="https://img.shields.io/badge/Local-LLM-red?style=for-the-badge&logo=python" alt="Local LLM">
  <img src="https://img.shields.io/badge/RAG-System-purple?style=for-the-badge" alt="RAG System">
  <img src="https://img.shields.io/badge/Structured-Tests-teal?style=for-the-badge" alt="Structured Tests">
</div>

<p align="center">
  <strong>Transform your S32K144 test scripts into fully automated SWTBot UI tests with locally-trained AI models</strong>
</p>

---

## 🎯 Overview

**AI CodeGen for NXP S32DS Testing** is an innovative AI-powered tool specifically designed for NXP's S32 Design Studio (S32DS) Eclipse-based IDE, with specialized focus on **S32K144 SoC**. It automatically generates complete SWTBot test suites from structured test cases (test steps + expected results) using locally-trained open-source LLM models, ensuring data privacy and S32K144-specific domain expertise.

### ✨ What it does

- **Input**: Natural language test scripts for S32DS workflows and UI interactions
- **Output**: Complete Maven project with S32DS-specific SWTBot UI tests
- **Magic**: Locally-trained AI models generate S32DS-aware SWTBot code with domain expertise

## 🚀 Key Features

- **🏠 Local AI Models**: Uses open-source LLMs trained specifically on S32DS patterns and workflows
- **🔒 Data Privacy**: All processing happens locally - no external API calls or data sharing
- **🎯 S32DS Specialized**: Deep understanding of S32DS UI components, workflows, and best practices
- **📦 Complete Maven Project**: Generates ready-to-run Maven projects with S32DS-specific configurations
- **🔧 Zero Manual Coding**: Eliminates the need to write SWTBot test code manually
- **⚡ Rapid Test Development**: Transforms hours of S32DS test coding into minutes of AI generation
- **🧠 RAG-Enhanced**: Retrieval-Augmented Generation with curated S32DS knowledge base
- **📋 S32DS Workflows**: Supports project creation, debugging, building, and deployment scenarios

## 🛠️ How It Works

```mermaid
graph LR
    A[S32DS Test Scripts] --> B[RAG System]
    B --> C[Local LLM Model]
    C --> D[S32DS SWTBot Code]
    D --> E[Maven Project]
    E --> F[Ready-to-Run S32DS Tests]

    subgraph "Local Training"
        G[S32DS Knowledge Base] --> H[Fine-tuned LLM]
        H --> C
    end
```

1. **Input Phase**: Provide S32DS test scenarios in natural language
2. **RAG Retrieval**: System retrieves relevant S32DS patterns from knowledge base
3. **Local AI Processing**: Locally-trained LLM generates S32DS-specific SWTBot code
4. **Code Generation**: Creates SWTBot test classes with S32DS UI element mappings
5. **Maven Project Creation**: Generates complete project with S32DS dependencies
6. **Ready to Execute**: Tests are ready to run against S32DS IDE

## 📋 Prerequisites

- **Java 8+**: Required for running SWTBot tests
- **Maven 3.6+**: For project build and dependency management
- **NXP S32DS**: Target application for UI testing (S32DS 3.4+)
- **Python 3.8+**: For local LLM training and RAG system
- **CUDA/GPU**: Recommended for local LLM training (optional)
- **SWTBot Framework**: Automatically included in generated projects

## 🚦 Quick Start

### 1. Prepare Your S32DS Test Scripts
Create S32DS-specific test descriptions with test steps and expected results:
```
Test Case: Create New S32K144 Project

Test Step | Expected Result
----------|----------------
Open S32DS IDE | S32DS IDE launches successfully with welcome screen
Navigate to File > New > S32DS Project | New S32DS Project wizard opens
Select "S32K1 SDK" project type | S32K1 SDK option is highlighted and selectable
Enter project name "TestProject_S32K144" | Project name field shows "TestProject_S32K144"
Configure target processor S32K144 | Processor dropdown shows S32K144 selected
Select package type LQFP100 | Package field displays LQFP100
Click Finish | Project creation progress dialog appears
Wait for project creation | Project appears in Project Explorer tree
Verify S32K144 configuration | Project properties show S32K144 processor settings
```

### 2. Generate S32DS SWTBot Tests
Run the AI CodeGen tool with your structured S32DS test scripts:
```bash
# Local RAG + LLM Implementation with S32DS knowledge base
python -m s32ds_codegen.main \
  --input s32ds_test_cases.csv \
  --output generated_tests/ \
  --model local \
  --soc S32K144 \
  --format structured
```

### 3. Execute Generated Tests
Navigate to the generated Maven project and run:
```bash
cd generated-swtbot-project
mvn clean test
```

## 📁 Generated S32DS Project Structure

```
generated-s32ds-swtbot-project/
├── pom.xml                          # S32DS-specific dependencies
├── src/
│   ├── main/java/
│   └── test/java/
│       └── com/nxp/s32ds/tests/
│           ├── ProjectTest.java             # Project creation tests (supports S32K144, etc.)
│           ├── BuildTest.java               # Build configuration tests
│           ├── DebugTest.java               # Debug setup tests
│           ├── PeripheralTest.java          # GPIO, CAN, UART tests
│           └── utils/
│               ├── SoCTestUtils.java        # SoC-specific utilities (S32K144, etc.)
│               └── S32DSTestBase.java       # Base test class
├── test-data/
│   ├── soc-configs/                # SoC configuration files (S32K144, etc.)
│   └── expected-results/           # Expected test outcomes
└── target/
```

## 🧪 Example Generated S32DS SWTBot Code

The AI generates clean, maintainable S32DS SWTBot code with separated step functions and parameterized SoC support:

```java
@Test
public void testCreateProject() {
    // Test configuration
    String soc = "S32K144";
    String packageType = "LQFP100";
    String projectName = "TestProject_" + soc;

    openS32DSIde(soc, packageType, projectName);
    openNewProjectWizard(soc, packageType, projectName);
    selectSDKType(soc, packageType, projectName);
    enterProjectName(soc, packageType, projectName);
    configureTargetProcessor(soc, packageType, projectName);
    selectPackageType(soc, packageType, projectName);
    finishProjectCreation(soc, packageType, projectName);
    waitForProjectCreation(soc, packageType, projectName);
    verifyProjectConfiguration(soc, packageType, projectName);
}

private void openNewProjectWizard(String soc, String packageType, String projectName) {
    // Test Step: Navigate to File > New > S32DS Project
    bot.menu("File").menu("New").menu("S32DS Project").click();
    // Expected Result: New S32DS Project wizard opens
    bot.waitUntil(shellIsActive("New S32DS Project"));
    assertTrue("New S32DS Project wizard should be active",
               bot.shell("New S32DS Project").isActive());
}

    // Test Step 3: Configure S32K144 processor
    bot.comboBoxWithLabel("Processor:").setSelection("S32K144");
    // Expected Result: Processor dropdown shows S32K144 selected
    assertEquals("S32K144 should be selected", "S32K144",
                 bot.comboBoxWithLabel("Processor:").selection());

    // Test Step 4: Set project name
    bot.textWithLabel("Project name:").setText("TestProject_S32K144");
    // Expected Result: Project name field shows TestProject_S32K144
    assertEquals("Project name should be set", "TestProject_S32K144",
                 bot.textWithLabel("Project name:").getText());

    // Test Step 5: Finish project creation
    bot.button("Finish").click();
    bot.waitUntil(shellCloses(bot.shell("New S32DS Project")));
    // Expected Result: Project appears in Project Explorer tree
    assertTrue("Project should appear in explorer",
               bot.tree().hasItems("TestProject_S32K144"));
}
```

## 🔧 S32DS Configuration

The generated Maven projects include:
- **SWTBot Dependencies**: Latest compatible versions for S32DS
- **JUnit Integration**: Ready for S32DS test execution with multiple SoCs
- **Maven Surefire Plugin**: Configured for S32DS UI testing
- **SoC Test Utilities**: Generated SoC-specific helper classes (S32K144, S32G, etc.)
- **SoC Specifications**: Built-in hardware knowledge for supported SoCs
- **Package Support**: Multiple package configurations (LQFP100, LQFP144, MAPBGA100, etc.)

## 🎯 Supported S32DS Test Scenarios

- ✅ **Project Management**: New project creation, import, SoC configuration (S32K144 example)
- ✅ **Build & Compile**: SoC-specific build configurations, ARM Cortex settings
- ✅ **Debug & Flash**: Debug configurations for various SoCs, flash programming, breakpoints
- ✅ **Peripheral Configuration**: GPIO, FlexCAN, UART, SPI, I2C, ADC setup
- ✅ **Pin Muxing**: Pin configuration for different packages, signal routing
- ✅ **Clock Configuration**: PLL setup, peripheral clocks, frequency settings
- ✅ **SDK Integration**: SDK installation, SoC component selection, code generation
- ✅ **Memory Configuration**: Flash and RAM memory mapping for different SoCs
- ✅ **Error Handling**: S32K144-specific build errors, debug issues, configuration problems

## 📚 Comprehensive Documentation

This project includes extensive documentation covering all aspects of the RAG + LLM system:

### 🔍 **Analysis & Research**
- **[System Analysis](docs/analysis/system-analysis.md)** - Complete analysis of RAG + LLM approach for S32DS
- **[RAG Architecture](docs/analysis/rag-architecture.md)** - Detailed RAG system design and implementation
- **[Technology Stack](docs/analysis/technology-stack.md)** - Local LLM training and deployment technologies
- **[S32DS Patterns](docs/research/s32ds-patterns.md)** - Analysis of S32K144 UI patterns and SWTBot mappings
- **[SWTBot Patterns](docs/research/swtbot-patterns.md)** - General SWTBot code patterns and best practices

### 🏗️ **Architecture & Design**
- **[System Overview](docs/architecture/system-overview.md)** - High-level architecture and component design
- **[Local LLM Training](docs/architecture/local-llm-training.md)** - S32K144-specific model training approach

### 🚀 **Implementation & Development**
- **[Development Plan](docs/implementation/development-plan.md)** - 12-week implementation roadmap
- **[Team Tasks](docs/implementation/team-tasks.md)** - Detailed task distribution for 4-person team
- **[Structured Test Format](docs/implementation/structured-test-format.md)** - S32K144 test case format with steps and expected results

### 👥 **Project Management**
- **[Team Structure](docs/project-management/team-structure.md)** - Roles, responsibilities, and collaboration

### 📖 **Getting Started**
- **[Documentation Overview](docs/README.md)** - Complete documentation navigation guide

## 🤝 Contributing

We welcome contributions to make AI CodeGen even better!

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

This project is developed and maintained by the **AI code gen** team - a dedicated group of developers passionate about revolutionizing UI testing through artificial intelligence and automation.

## 🙏 Acknowledgments

- **Eclipse SWTBot Team**: For the amazing UI testing framework
- **AI/ML Community**: For advancing automated code generation
- **AI code gen Team**: For bringing this innovative solution to life
- **Contributors**: Everyone who helps improve this project

## 📞 Support & Contact

- 🐛 **Issues**: [GitHub Issues](../../issues)
- 💬 **Discussions**: [GitHub Discussions](../../discussions)
- 📧 **Email**: [Your contact email]

---

<div align="center">
  <p><strong>Made with ❤️ by the AI code gen Team for the Eclipse Testing Community</strong></p>
  <p>⭐ Star this repository if it helps you automate your UI testing!</p>
</div>
